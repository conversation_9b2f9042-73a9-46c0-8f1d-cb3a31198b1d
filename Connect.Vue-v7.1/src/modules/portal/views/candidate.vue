<template>
	<div class="wrapper">
		<el-row class="opt">
			<el-col :span="10">
				<el-button @click="showAddDialog" type="primary">添加</el-button>
			</el-col>
			<el-col :span="14" style="text-align: right; display: flex; align-items: center; justify-content: flex-end; gap: 10px;">
				<el-input
					placeholder="请输入标题或URL"
					v-model="search.s"
					style="width: 300px"
					clearable
					@clear="refresh"
				>
					<template #append>
						<el-button :icon="Search" @click="refresh" />
					</template>
				</el-input>
			</el-col>
		</el-row>
		<el-row class="list">
			<el-col :span="24">
				<el-table
					border
					style="width: 100%"
					:data="tableData"
					:max-height="tableHeight"
					:row-style="{ height: '40px' }"
					:header-cell-style="{ background: '#ebeef5', color: '#333' }"
				>
					<el-table-column fixed prop="id" label="ID" width="70" align="center">
					</el-table-column>
					<el-table-column label="审核" width="85" align="center">
						<template #default="scope">
							<el-tag :type="scope.row.approvedBy ? 'success' : 'warning'">
								{{ scope.row.approvedBy ? '已审核' : '待审核' }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column label="Url" width="240" align="left" :show-overflow-tooltip="true">
						<template #default="scope">
							<el-link :href="scope.row.url" target="_blank" type="primary" v-if="scope.row.url">
								{{ getUrlDisplayText(scope.row) }}
							</el-link>
							<span v-else>-</span>
						</template>
					</el-table-column>
					<el-table-column label="标题" width="250" align="left" :show-overflow-tooltip="true">
						<template #default="scope">
							<el-link
								v-if="scope.row.wwwArticleID"
								:href="`https://www.chasedream.com/article/${scope.row.wwwArticleID}`"
								target="_blank"
								type="primary"
							>
								{{ scope.row.title || '-' }}
							</el-link>
							<span v-else>{{ scope.row.title || '-' }}</span>
						</template>
					</el-table-column>
					<el-table-column label="作者" width="100" align="center">
						<template #default="scope">
							{{ scope.row.author || '-' }}
						</template>
					</el-table-column>
					<el-table-column label="频道" width="120" align="center">
						<template #default="scope">
							{{ scope.row.channel || '-' }}
						</template>
					</el-table-column>
					<el-table-column label="标签" width="120" align="center">
						<template #default="scope">
							{{ scope.row.tag || '-' }}
						</template>
					</el-table-column>
					<el-table-column label="添加人/审核人" width="120" align="center">
						<template #default="scope">
							<div>
								<div v-if="scope.row.addedBy">{{ scope.row.addedBy }}</div>
								<div v-if="scope.row.approvedBy" style="color: #67c23a;">{{ scope.row.approvedBy }}</div>
								<span v-if="!scope.row.addedBy && !scope.row.approvedBy">-</span>
							</div>
						</template>
					</el-table-column>
					<el-table-column label="审核时间" width="150" align="center">
						<template #default="scope">
							{{ scope.row.approvedTime ? formatter(scope.row.approvedTime) : '-' }}
						</template>
					</el-table-column>
					<el-table-column label="创建时间" width="150" align="center">
						<template #default="scope">
							{{ scope.row.createTime ? formatter(scope.row.createTime) : '-' }}
						</template>
					</el-table-column>
					<el-table-column fixed="right" label="操作" width="100" align="center">
						<template #default="scope">
							<el-icon
								@click="editClick(scope.row)"
								class="cursor-pointer"
								style="color: #464bd7; margin-right: 8px"
								:size="20"
								><edit
							/></el-icon>

							<el-popconfirm
										title="确定要删除吗?"
										@confirm="deleteHandler(scope.row)"
									>
										<template #reference>
											<el-icon
												class="cursor-pointer"
												style="color: crimson; margin-right: 8px"
												:size="20"
												><delete
											/></el-icon>
										</template>
									</el-popconfirm>
						</template>
					</el-table-column>
				</el-table>
			</el-col>
		</el-row>
		<el-row class="page">
			<el-pagination
				class="pagination"
				background
				:currentPage="currentPage"
				:page-size="pageSize"
				layout="total, sizes, prev, pager, next, jumper"
				:total="total"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</el-row>

		<!-- 添加/编辑弹出框 -->
		<el-dialog
			v-model="dialogVisible"
			:title="isEdit ? '编辑候选文章' : '添加候选文章'"
			width="95%"
			:close-on-click-modal="false"
			@close="closeDialog"
			class="article-dialog"
			:show-close="false"
		>
			<template #header="{ close }">
				<div class="dialog-header">
					<div class="header-left">
						<div class="header-icon">
							<el-icon size="20" color="#409EFF">
								<Edit v-if="isEdit" />
								<Plus v-else />
							</el-icon>
						</div>
						<div class="header-text">
							<h3>{{ isEdit ? '编辑候选文章' : '添加候选文章' }}</h3>
							<p>{{ isEdit ? '修改文章信息并实时预览效果' : '填写文章信息并实时预览效果' }}</p>
						</div>
					</div>
					<el-button @click="close" :icon="Close" circle size="small" />
				</div>
			</template>

			<div class="article-editor-container">
				<!-- 左侧编辑区域 -->
				<div class="editor-left">
					<div class="editor-section">
						<div class="section-header">
							<el-icon><Document /></el-icon>
							<span>文章信息</span>
						</div>
						<div class="form-container">
							<el-form :model="form" label-width="90px" :rules="rules" ref="formRef" size="default">
								<div class="form-group">
									<el-form-item label="Url" :required="true" prop="url">
										<el-input
											v-model="form.url"
											placeholder="请输入文章URL地址"
											:prefix-icon="Link"
											clearable
										/>
									</el-form-item>

									<el-form-item label="标题" :required="true" prop="title">
										<el-input
											v-model="form.title"
											placeholder="请输入文章标题"
											:prefix-icon="EditPen"
											clearable
										/>
									</el-form-item>
								</div>

								<div class="form-group">
									<el-row :gutter="16">
										<el-col :span="12">
											<el-form-item label="版块" prop="board">
												<el-input
													v-model="form.board"
													placeholder="请输入版块"
													:prefix-icon="Collection"
													clearable
												/>
											</el-form-item>
										</el-col>
										<el-col :span="12">
											<el-form-item label="创建时间" prop="createTime">
												<el-date-picker
													v-model="form.createTime"
													type="datetime"
													placeholder="选择创建时间"
													format="YYYY-MM-DD HH:mm:ss"
													value-format="YYYY-MM-DD HH:mm:ss"
													style="width: 100%"
												/>
											</el-form-item>
										</el-col>
									</el-row>

									<el-row :gutter="16">
										<el-col :span="12">
											<el-form-item label="作者" prop="author">
												<el-input
													v-model="form.author"
													placeholder="请输入作者"
													:prefix-icon="User"
													clearable
												/>
											</el-form-item>
										</el-col>
										<el-col :span="12">
											<el-form-item label="频道" prop="channel">
												<el-input
													v-model="form.channel"
													placeholder="请输入频道"
													:prefix-icon="Promotion"
													clearable
												/>
											</el-form-item>
										</el-col>
									</el-row>

									<el-row :gutter="16">
										<el-col :span="12">
											<el-form-item label="标签" prop="tag">
												<el-input
													v-model="form.tag"
													placeholder="请输入标签"
													:prefix-icon="PriceTag"
													clearable
												/>
											</el-form-item>
										</el-col>
										<el-col :span="12">
											<el-form-item label="修复新闻" prop="fixNews">
												<el-switch
													v-model="form.fixNews"
													active-text="是"
													inactive-text="否"
												/>
											</el-form-item>
										</el-col>
									</el-row>
								</div>

								<div class="form-group">
									<el-form-item label="文章内容" prop="content">
										<el-input
											type="textarea"
											v-model="form.content"
											placeholder="请输入文章内容..."
											:rows="8"
											@input="updatePreview"
											show-word-limit
											maxlength="5000"
											resize="vertical"
										/>
									</el-form-item>

									<el-form-item label="参考内容" prop="refContent">
										<el-input
											type="textarea"
											v-model="form.refContent"
											placeholder="请输入参考内容..."
											:rows="4"
											show-word-limit
											maxlength="2000"
											resize="vertical"
										/>
									</el-form-item>
								</div>
							</el-form>
						</div>
					</div>
				</div>

				<!-- 右侧预览区域 -->
				<div class="editor-right">
					<div class="preview-header">
						<div class="preview-title">
							<el-icon><View /></el-icon>
							<span>实时预览</span>
						</div>
						<div class="preview-status">
							<el-tag type="warning" size="small">待审核</el-tag>
						</div>
					</div>
					<div class="preview-content">
						<div class="article-preview">
							<div class="article-header">
								<h2 class="article-title">{{ form.title || '文章标题' }}</h2>
								<div class="article-meta">
									<span v-if="form.author" class="meta-author">
										<el-icon><User /></el-icon>
										{{ form.author }}
									</span>
									<span v-if="form.channel" class="meta-channel">
										<el-icon><Promotion /></el-icon>
										{{ form.channel }}
									</span>
									<span v-if="form.tag" class="meta-tag">
										<el-icon><PriceTag /></el-icon>
										{{ form.tag }}
									</span>
								</div>
							</div>

							<div class="article-info" v-if="form.url">
								<div class="info-item">
									<span class="info-label">原文链接：</span>
									<el-link :href="form.url" target="_blank" type="primary" class="info-link">
										{{ form.url }}
									</el-link>
								</div>
							</div>

							<div class="article-body">
								<div class="content-section" v-if="form.content">
									<h4>文章内容</h4>
									<div class="content-text" v-html="previewContent"></div>
								</div>
								<div class="content-section" v-else>
									<div class="empty-content">
										<el-icon size="48" color="#C0C4CC"><Document /></el-icon>
										<p>暂无内容，请在左侧编辑区域输入文章内容</p>
									</div>
								</div>

								<div class="content-section" v-if="form.refContent">
									<h4>参考内容</h4>
									<div class="ref-content-text">{{ form.refContent }}</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<template #footer>
				<div class="dialog-footer">
					<el-button @click="closeDialog" size="large">
						<el-icon><Close /></el-icon>
						取消
					</el-button>
					<el-button type="primary" @click="handleSubmit(formRef)" size="large">
						<el-icon><Check /></el-icon>
						{{ isEdit ? '保存修改' : '创建文章' }}
					</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus";
import {
	Edit, Delete, Search, Plus, Close, Document, Link, EditPen,
	Collection, User, Promotion, PriceTag, View, Check
} from "@element-plus/icons-vue";
import { ref, reactive, onMounted, nextTick } from "vue";
import { useCool } from "/@/cool";
import { removeEmptyFromObject } from "/@/cool/utils";
import { datelineToDate } from "/@/cool/utils";
import type { FormInstance } from "element-plus";

const { service } = useCool();

const search = reactive({
	s: ""
});

const tableHeight = ref(0);
const tableData = ref([]);
const currentPage = ref(1);
const pageSize = ref(50);
const total = ref(0);

// 弹出框相关数据
const dialogVisible = ref(false);
const isEdit = ref(false);

const form = reactive({
	id: 0,
	url: "",
	title: "",
	board: "",
	author: "",
	channel: "",
	tag: "",
	content: "",
	refContent: "",
	createTime: "",
	fixNews: false
});

const rules = reactive({
	url: [{ required: true, message: "请输入URL", trigger: "blur" }],
	title: [{ required: true, message: "请输入标题", trigger: "blur" }]
});

const formRef = ref<FormInstance>();

// 预览相关
const previewContent = ref('');

const formatter = (datetime: any) => {
	if (!datetime) return '-';
	return datelineToDate(new Date(datetime).getTime() / 1000, "YYYY-MM-DD HH:mm:ss");
};

// 获取URL显示文本：board字段 + / + 从URL中提取的数字
const getUrlDisplayText = (row: any) => {
	if (!row.url) return '-';

	// 从URL中提取数字，例如从 https://forum.chasedream.com/thread-1396377-1-1.html 提取 1396377
	const match = row.url.match(/thread-(\d+)/);
	const threadId = match ? match[1] : '';

	// 返回 board字段 + / + 提取的数字
	const board = row.board || '';
	return threadId ? `${board}/${threadId}` : (board || row.url);
};

// 更新预览内容
const updatePreview = () => {
	// 简单的HTML内容预览，将换行转换为<br>
	previewContent.value = form.content
		.replace(/\n/g, '<br>')
		.replace(/\t/g, '&nbsp;&nbsp;&nbsp;&nbsp;');
};

const handleSizeChange = (val: number) => {
	pageSize.value = val;
	refresh();
};

const handleCurrentChange = (val: number) => {
	currentPage.value = val;
	refresh();
};

const editClick = (row: any) => {
	showEditDialog(row.id);
};

const deleteHandler = (row: any) => {
	service.base.common.portal
		.candidateArticleDelete({
			id: row.id
		})
		.then(() => {
			ElMessage({
				message: "已删除!",
				type: "success"
			});
			refresh();
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const refresh = () => {
	const s = removeEmptyFromObject(search);

	service.base.common.portal
		.candidateArticlePage({
			...s,
			page: currentPage.value,
			pageSize: pageSize.value
		})
		.then((res) => {
			tableData.value = res[0];
			total.value = res[1];
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

// 弹出框控制方法
const showAddDialog = () => {
	isEdit.value = false;
	resetForm();
	dialogVisible.value = true;
};

const showEditDialog = (id: number) => {
	isEdit.value = true;
	resetForm();
	form.id = id;
	dialogVisible.value = true;
	nextTick(() => {
		loadData();
	});
};

const closeDialog = () => {
	dialogVisible.value = false;
	resetForm();
};

const resetForm = () => {
	Object.assign(form, {
		id: 0,
		url: "",
		title: "",
		board: "",
		author: "",
		channel: "",
		tag: "",
		content: "",
		refContent: "",
		createTime: "",
		fixNews: false
	});

	// 清除表单验证
	if (formRef.value) {
		formRef.value.clearValidate();
	}
};

// 加载编辑数据
const loadData = async () => {
	service.base.common.portal
		.candidateArticleFindOne({
			id: form.id
		})
		.then((res) => {
			Object.assign(form, res);
			updatePreview(); // 加载数据后更新预览
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

// 表单提交
const handleSubmit = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;

	await formEl.validate((isValid, _fields) => {
		if (!isValid) return;

		if (isEdit.value) {
			// 编辑模式
			service.base.common.portal
				.candidateArticleUpdate({
					...form
				})
				.then(() => {
					ElMessage({
						message: "已修改!",
						type: "success"
					});
					closeDialog();
					refresh();
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		} else {
			// 添加模式
			service.base.common.portal
				.candidateArticleCreate({
					...form
				})
				.then(() => {
					ElMessage({
						message: "已添加!",
						type: "success"
					});
					closeDialog();
					refresh();
				})
				.catch((err) => {
					ElMessage.error(err.message);
				});
		}
	});
};

refresh();

onMounted(() => {
	nextTick(() => {
		tableHeight.value = window.innerHeight - 250;
		window.onresize = () => {
			tableHeight.value = window.innerHeight - 250;
		};
	});
});
</script>

<style lang="scss" scoped>
@import "../css/index.scss";

// 文章编辑器样式
.article-dialog {
	:deep(.el-dialog) {
		margin: 20px auto;
		max-height: calc(100vh - 40px);
		border-radius: 12px;
		box-shadow: 0 12px 32px 4px rgba(0, 0, 0, 0.04), 0 8px 20px rgba(0, 0, 0, 0.08);
	}

	:deep(.el-dialog__header) {
		padding: 0;
		margin: 0;
	}

	:deep(.el-dialog__body) {
		padding: 0;
		height: calc(100vh - 200px);
	}

	:deep(.el-dialog__footer) {
		padding: 0;
	}
}

.dialog-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20px 24px;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border-radius: 12px 12px 0 0;

	.header-left {
		display: flex;
		align-items: center;
		gap: 16px;

		.header-icon {
			width: 48px;
			height: 48px;
			background: rgba(255, 255, 255, 0.2);
			border-radius: 12px;
			display: flex;
			align-items: center;
			justify-content: center;
			backdrop-filter: blur(10px);
		}

		.header-text {
			h3 {
				margin: 0;
				font-size: 20px;
				font-weight: 600;
				color: white;
			}

			p {
				margin: 4px 0 0 0;
				font-size: 14px;
				color: rgba(255, 255, 255, 0.8);
			}
		}
	}
}

.article-editor-container {
	display: flex;
	height: 100%;
	background: #f8fafc;
	overflow: hidden;
}

.editor-left {
	width: 50%;
	border-right: 1px solid #e2e8f0;
	display: flex;
	flex-direction: column;
	background: white;
}

.editor-right {
	width: 50%;
	display: flex;
	flex-direction: column;
	background: #f8fafc;
}

.editor-section {
	flex: 1;
}

.section-header {
	display: flex;
	align-items: center;
	gap: 8px;
	padding: 16px 24px;
	background: #f1f5f9;
	border-bottom: 1px solid #e2e8f0;
	font-weight: 600;
	color: #475569;
	font-size: 14px;

	.el-icon {
		color: #3b82f6;
	}
}

.form-container {
	padding: 24px;
	overflow-y: auto;

	.form-group {
		margin-bottom: 24px;

		&:last-child {
			margin-bottom: 0;
		}
	}

	:deep(.el-form-item) {
		margin-bottom: 20px;

		.el-form-item__label {
			color: #374151;
			font-weight: 500;
		}

		.el-input__wrapper {
			border-radius: 8px;
			box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
			transition: all 0.2s;

			&:hover {
				box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
			}

			&.is-focus {
				box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
			}
		}

		.el-textarea__inner {
			border-radius: 8px;
			box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
			transition: all 0.2s;

			&:hover {
				box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
			}

			&:focus {
				box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
			}
		}
	}
}

.preview-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 16px 24px;
	background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
	border-bottom: 1px solid #e2e8f0;

	.preview-title {
		display: flex;
		align-items: center;
		gap: 8px;
		font-weight: 600;
		color: #475569;
		font-size: 14px;

		.el-icon {
			color: #10b981;
		}
	}

	.preview-status {
		.el-tag {
			border-radius: 6px;
		}
	}
}

.preview-content {
	flex: 1;
	overflow-y: auto;
	padding: 24px;
}

.article-preview {
	background: white;
	border-radius: 12px;
	box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
	overflow: hidden;
}

.article-header {
	padding: 24px;
	border-bottom: 1px solid #e2e8f0;

	.article-title {
		margin: 0 0 16px 0;
		font-size: 24px;
		font-weight: 700;
		color: #1f2937;
		line-height: 1.3;
	}

	.article-meta {
		display: flex;
		flex-wrap: wrap;
		gap: 16px;

		span {
			display: flex;
			align-items: center;
			gap: 6px;
			font-size: 14px;
			color: #6b7280;

			.el-icon {
				color: #9ca3af;
			}
		}
	}
}

.article-info {
	padding: 16px 24px;
	background: #f8fafc;
	border-bottom: 1px solid #e2e8f0;

	.info-item {
		display: flex;
		align-items: center;
		gap: 8px;
		font-size: 14px;

		.info-label {
			color: #6b7280;
			font-weight: 500;
		}

		.info-link {
			font-weight: 500;
		}
	}
}

.article-body {
	padding: 24px;
}

.content-section {
	margin-bottom: 24px;

	&:last-child {
		margin-bottom: 0;
	}

	h4 {
		margin: 0 0 12px 0;
		font-size: 16px;
		font-weight: 600;
		color: #374151;
		padding-bottom: 8px;
		border-bottom: 2px solid #e5e7eb;
	}
}

.content-text, .ref-content-text {
	background: #f9fafb;
	border: 1px solid #e5e7eb;
	border-radius: 8px;
	padding: 16px;
	font-size: 14px;
	line-height: 1.6;
	color: #374151;
	white-space: pre-wrap;
	min-height: 120px;
}

.empty-content {
	text-align: center;
	padding: 48px 24px;
	color: #9ca3af;

	p {
		margin: 16px 0 0 0;
		font-size: 14px;
	}
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 12px;
	padding: 20px 24px;
	border-top: 1px solid #e2e8f0;
	background: white;
	border-radius: 0 0 12px 12px;

	.el-button {
		border-radius: 8px;
		font-weight: 500;
		padding: 12px 24px;

		&.el-button--primary {
			background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
			border: none;
			box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.25);

			&:hover {
				background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
				transform: translateY(-1px);
				box-shadow: 0 6px 8px -1px rgba(59, 130, 246, 0.35);
			}
		}

		&:not(.el-button--primary) {
			border: 1px solid #d1d5db;
			color: #6b7280;

			&:hover {
				border-color: #9ca3af;
				color: #374151;
				transform: translateY(-1px);
			}
		}
	}
}

// 响应式设计
@media (max-width: 1400px) {
	.article-editor-container {
		flex-direction: column;
		height: auto;
	}

	.editor-left, .editor-right {
		width: 100%;
	}

	.editor-left {
		border-right: none;
		border-bottom: 1px solid #e2e8f0;
		max-height: 60vh;
	}

	.editor-right {
		max-height: 40vh;
	}

	.article-dialog {
		:deep(.el-dialog) {
			width: 95% !important;
			max-width: none;
		}

		:deep(.el-dialog__body) {
			height: 80vh;
		}
	}
}

@media (max-width: 768px) {
	.dialog-header {
		padding: 16px 20px;

		.header-left {
			gap: 12px;

			.header-icon {
				width: 40px;
				height: 40px;
			}

			.header-text {
				h3 {
					font-size: 18px;
				}

				p {
					font-size: 13px;
				}
			}
		}
	}

	.form-container {
		padding: 16px;
	}

	.preview-content {
		padding: 16px;
	}

	.article-header {
		padding: 16px;

		.article-title {
			font-size: 20px;
		}
	}

	.article-body {
		padding: 16px;
	}

	.dialog-footer {
		padding: 16px 20px;

		.el-button {
			padding: 10px 20px;
		}
	}
}
</style>